# ThankView API Production Deployment Guide

## Overview

This directory contains the production deployment configuration for the ThankView API using GitHub Actions and Ansible. The deployment system automatically provisions and updates production API servers on AWS EC2 instances.

## Architecture

### Infrastructure Components
- **AWS EC2 Instances**: Production API servers tagged with `Environment: prod` and `Name: *api*`
- **GitHub Actions**: Self-hosted runner on EC2 for deployment orchestration
- **Ansible**: Configuration management and deployment automation
- **AWS Secrets Manager**: Environment configuration storage
- **AWS SSM Parameter Store**: SSH key management

### Deployment Flow

```mermaid
graph TD
    A[Developer] -->|Manual Trigger| B[GitHub Actions Workflow]
    B --> C[Self-hosted Runner]
    C --> D[Install Ansible & Dependencies]
    D --> E[Configure AWS Credentials]
    E --> F[Discover EC2 Instances]
    F --> G[Run Ansible Playbook]
    G --> H[Deploy to API Servers]
    H --> I[Update Environment Files]
    I --> J[Restart Services]
```

## Files Structure

```
thankview-deploy-production/
├── README.md                    # This file
├── ansible.cfg                  # Ansible configuration
├── aws_ec2.aws_ec2.yml         # AWS EC2 dynamic inventory
├── api-deploy-main.yml         # Main API deployment playbook
└── main-deploy.yml             # Master deployment orchestrator
```

## How to Deploy

### Prerequisites
1. Access to GitHub repository with appropriate permissions
2. Required GitHub Secrets configured (see [Secrets](#required-secrets))
3. Target EC2 instances properly tagged
4. Self-hosted GitHub Actions runner configured

### Manual Deployment Steps

1. **Navigate to GitHub Actions**
   - Go to the repository's Actions tab
   - Select "Deploy Production API" workflow

2. **Configure Deployment Parameters**
   - **Environment**: `production` (fixed)
   - **Deploy Target**: `prod_api_host` (API servers)
   - **Deploy Branch**: Branch to deploy from (default: `production`)

3. **Execute Deployment**
   - Click "Run workflow"
   - Monitor progress in the Actions tab

### Deployment Process

The workflow performs these steps:

1. **Setup Phase**
   - Checkout code from specified branch
   - Fetch deployment scripts from production branch
   - Install Python dependencies and Ansible

2. **Discovery Phase**
   - Configure AWS credentials
   - Discover EC2 instances using dynamic inventory
   - Verify target servers are accessible

3. **Deployment Phase**
   - Clone/update application code on target servers
   - Fetch environment configuration from AWS Secrets Manager
   - Set application role based on EC2 instance tags
   - Clear Laravel caches
   - Restart PHP-FPM service

## Configuration Details

### Dynamic Inventory (`aws_ec2.aws_ec2.yml`)
- Discovers EC2 instances across multiple AWS regions
- Filters for running instances with `Environment: prod` tag
- Groups servers by function (API, workers, etc.)
- Uses private IP addresses for communication

### Server Groups
- `prod_api_host`: API servers (Name tag contains 'api')
- `webservers`: Web servers (Name tag contains 'webserver-prod')
- `video_workers`: Video processing workers
- `send_servers`: Email/notification servers

### Environment Management
- Environment variables stored in AWS Secrets Manager
- Different secrets for different server types:
  - `prod/api-host-env`: Standard API servers
  - `prod/ca-api-host-env`: Canadian API servers
- `APP_ROLE` set dynamically from EC2 instance tags

## Required Secrets

Configure these in GitHub repository settings:

| Secret Name | Description |
|-------------|-------------|
| `PRODUCTION_AWS_ACCESS_KEY_ID` | AWS access key for deployment |
| `PRODUCTION_AWS_SECRET_ACCESS_KEY` | AWS secret key for deployment |
| `PRODUCTION_DEPLOY_SSH_PRIVATE_KEY` | SSH private key for server access |

## Monitoring and Troubleshooting

### Common Issues

1. **Ansible Command Not Found**
   - Ensure PATH includes `~/.local/bin`
   - Verify Ansible installation completed successfully

2. **SSH Connection Failures**
   - Check SSH key configuration in AWS SSM Parameter Store
   - Verify security groups allow SSH access from runner

3. **Environment File Issues**
   - Confirm secrets exist in AWS Secrets Manager
   - Check IAM permissions for Secrets Manager access

### Logs and Debugging
- View detailed logs in GitHub Actions workflow runs
- Use `ansible-inventory --graph` to verify server discovery
- Check individual task outputs for specific failures

## Security Considerations

- SSH keys stored securely in AWS SSM Parameter Store
- Environment variables managed through AWS Secrets Manager
- Private IP communication between runner and target servers
- No sensitive data stored in repository

## Maintenance

### Regular Tasks
- Update Ansible collections periodically
- Review and rotate SSH keys
- Monitor AWS resource usage
- Update deployment scripts as needed

### Emergency Procedures
- Rollback: Deploy previous stable branch
- Hotfix: Deploy from hotfix branch with same process
- Manual intervention: SSH directly to servers if needed

---

For questions or issues, contact the DevOps team or create an issue in the repository.
